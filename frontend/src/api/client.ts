import {
  User,
  UserCreate,
  Deployment,
  DeploymentCreate,
  DeploymentUpdate,
  CloudProvider,
  Region,
  InstanceType,
  Package,
  PackageConfig,
  PricingCalculation,
  ServerInfo
} from './types';

// Use relative URLs when in development (proxy) or production
const API_BASE_URL = '/api';

class APIClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    // Get token from localStorage for authenticated requests
    const token = localStorage.getItem('token');

    // Get API key - make sure it's properly accessed
    const apiKey = import.meta.env.VITE_API_SECRET_KEY;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        'X-API-Key': apiKey || '', // Always include this header
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }
    return await response.json();
  }

  // Authentication
  async login(email: string, password: string): Promise<{ access_token: string; token_type: string }> {
    // Your backend uses form data instead of JSON
    const formData = new URLSearchParams();
    formData.append('username', email); // Backend expects 'username' field but wants email value
    formData.append('password', password);

    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData.toString(),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  }

  async logout(): Promise<void> {
    return this.request<void>('/auth/logout', {
      method: 'POST',
    });
  }

  async getCurrentUser(): Promise<User> {
    // Try to get current user - might be /auth/me or /users/me
    try {
      return this.request<User>('/auth/me');
    } catch (error) {
      // Fall back to /users/me if /auth/me doesn't exist
      return this.request<User>('/users/me');
    }
  }

  // User Management
  async createUser(userData: UserCreate): Promise<void> {
    await this.request<void>('/users/', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async resendVerification(): Promise<{ message: string }> {
    return this.request<{ message: string }>('/users/resend-verification', {
      method: 'POST',
    });
  }

  async verifyEmailToken(token: string): Promise<{ message: string }> {
    const encoded = encodeURIComponent(token);
    return this.request<{ message: string }>(`/auth/verify-email?token=${encoded}`);
  }

  async getUsers(): Promise<User[]> {
    return this.request<User[]>('/users/');
  }

  async getUser(userId: number): Promise<User> {
    return this.request<User>(`/users/${userId}`);
  }

  async deleteUser(userId: number): Promise<void> {
    return this.request<void>(`/users/${userId}`, {
      method: 'DELETE',
    });
  }

  async createUserAsAdmin(userData: UserCreate & { is_admin: boolean }): Promise<User> {
    return this.request<User>('/users/admin-create', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async makeUserAdmin(userId: number): Promise<User> {
    return this.request<User>(`/users/${userId}/make-admin`, {
      method: 'PATCH',
    });
  }

  async removeAdminRole(userId: number): Promise<User> {
    return this.request<User>(`/users/${userId}/remove-admin`, {
      method: 'PATCH',
    });
  }

  // Deployment Management
  async getDeployments(): Promise<Deployment[]> {
    return this.request<Deployment[]>('/deployments/');
  }

  async getDeploymentsByUser(userId: number): Promise<Deployment[]> {
    return this.request<Deployment[]>(`/deployments/user/${userId}`);
  }

  // Utility function to generate client_name and client_id
  private generateClientDetails(userEmail: string, username: string): { client_name: string; client_id: string } {
    const timestamp = Date.now().toString().slice(-6);
    const emailPrefix = userEmail.split('@')[0].replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
    const usernamePrefix = username.replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);

    const client_name = `${emailPrefix || usernamePrefix}-${timestamp}`;
    const client_id = `client_${emailPrefix || usernamePrefix}-${timestamp}`;

    return { client_name, client_id };
  }

  // Deployment creation - can be mocked or real based on environment flag
  async createDeployment(deploymentData: DeploymentCreate, userEmail: string, username: string): Promise<Deployment> {
    // Check if we should mock deployment creation (default: true for safety)
    const shouldMockDeployment = import.meta.env.VITE_MOCK_DEPLOYMENT_CREATION !== 'false';

    // Generate client details
    const { client_name, client_id } = this.generateClientDetails(userEmail, username);

    // Determine postgres host based on package type
    const postgresHost = deploymentData.package === 'Pangolin+AI'
      ? 'komodo-postgres-1'
      : import.meta.env.VITE_POSTGRES_HOST || 'pangolin-postgres';

    // Add environment variables and generated fields to deployment data
    const enrichedDeploymentData: DeploymentCreate = {
      ...deploymentData,
      client_name,
      client_id,
      postgres_host: postgresHost,
      postgres_user: import.meta.env.VITE_POSTGRES_USER || 'REPLACE_ENV_VARIABLE',
      postgres_password: import.meta.env.VITE_POSTGRES_PASSWORD || 'REPLACE_ENV_VARIABLE',
      github_repo: import.meta.env.VITE_GITHUB_REPO || 'oidebrett/manidae'
    };

    if (shouldMockDeployment) {
      // Mock the deployment creation to avoid real cloud infrastructure costs
      console.log('🚨 MOCKING deployment creation to avoid costs:', enrichedDeploymentData);
      console.log('💡 To enable real deployment creation, set VITE_MOCK_DEPLOYMENT_CREATION=false');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Return a mock deployment response
      const mockDeployment: Deployment = {
        ...enrichedDeploymentData,
        id: Math.floor(Math.random() * 10000),
        user_id: 1, // Mock user ID
        cost: Math.floor(Math.random() * 100) + 50, // Mock cost between 50-150
        deleted_at: null,
        status: 'CREATING',
        created_at: new Date().toISOString(), // Add created_at for sorting
      };

      return mockDeployment;
    } else {
      // Make real API call to create deployment
      console.log('🚀 Creating REAL deployment:', enrichedDeploymentData);
      console.warn('⚠️  This will create actual cloud infrastructure and incur costs!');

      return this.request<Deployment>('/deployments/', {
        method: 'POST',
        body: JSON.stringify(enrichedDeploymentData),
      });
    }
  }

  async updateDeployment(deploymentId: number, updateData: DeploymentUpdate): Promise<Deployment> {
    return this.request<Deployment>(`/deployments/${deploymentId}`, {
      method: 'PATCH',
      body: JSON.stringify(updateData),
    });
  }

  async deleteDeployment(deploymentId: number): Promise<Deployment> {
    return this.request<Deployment>(`/deployments/${deploymentId}`, {
      method: 'DELETE',
    });
  }

  async getDeploymentStatus(deploymentId: number): Promise<string> {
    return this.request<string>(`/deployments/${deploymentId}/status`);
  }

  // Configuration Data
  async getPackages(): Promise<Package[]> {
    return this.request<Package[]>('/deployments/packages/');
  }

  async getPackageConfig(packageName: string): Promise<PackageConfig> {
    return this.request<PackageConfig>(`/deployments/packages/${packageName}/config`);
  }

  async getCloudProviders(): Promise<CloudProvider[]> {
    return this.request<CloudProvider[]>('/deployments/cloud-providers/');
  }

  async getRegions(provider: string): Promise<Region[]> {
    return this.request<Region[]>(`/deployments/regions/${provider}`);
  }

  async getInstanceTypes(provider: string, region: string): Promise<InstanceType[]> {
    return this.request<InstanceType[]>(`/deployments/instance-types/${provider}/${region}`);
  }

  // Pricing
  async calculatePricing(deploymentData: DeploymentCreate): Promise<PricingCalculation> {
    return this.request<PricingCalculation>('/deployments/pricing/calculate', {
      method: 'POST',
      body: JSON.stringify(deploymentData),
    });
  }


  // Servers (BYOVPS)
  async listServers(): Promise<ServerInfo[]> {
    return this.request<ServerInfo[]>('/servers/');
  }

  async validateVps(ip_address: string): Promise<{ status: string; message: string }> {
    return this.request<{ status: string; message: string }>(`/servers/validate-vps`, {
      method: 'POST',
      body: JSON.stringify({ ip_address }),
    });
  }

  async getPeripheryScript(): Promise<{ script: string }> {
    return this.request<{ script: string }>(`/servers/periphery-script`);
  }

  // Billing
  async getTransactions(): Promise<{ id: number; user_id: number; amount: number; type: string; description?: string; created_at: string; }[]> {
    return this.request(`/billing/transactions`);
  }

  async adjustUserBalance(userId: number, amount: number, description?: string) {
    return this.request(`/billing/admin/users/${userId}/adjust-balance`, {
      method: 'POST',
      body: JSON.stringify({ amount, description }),
    });
  }

  async createStripeCheckoutSession(amountCents: number): Promise<{ url: string }> {
    return this.request(`/billing/stripe/create-checkout-session`, {
      method: 'POST',
      body: JSON.stringify({ amount_cents: amountCents }),
    });
  }

}

export const apiClient = new APIClient();
export default apiClient;
