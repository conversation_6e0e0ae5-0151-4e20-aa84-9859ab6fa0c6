import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts';
import { ModeToggle } from './ThemeToggle';
import { Button } from './ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from './ui/dropdown-menu';
import SetupModal from './SetupModal';

interface Props {
  children: React.ReactNode;
}

const Layout: React.FC<Props> = ({ children }) => {
  const { isAuthenticated, user, logout } = useAuth();

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b bg-background">
        <div className="flex h-16 items-center justify-between px-6">
          <div className="flex items-center gap-3">
            <Link to="/" className="flex items-center gap-2">
              <img 
                src="/manadae.png" 
                alt="Manidae Cloud" 
                className="h-7 w-auto"
              />
              <span className="text-xl font-bold text-foreground">Man<PERSON></span>
              <span className="hidden sm:inline rounded bg-muted px-2 py-0.5 text-xs text-muted-foreground">
                Cloud Beta
              </span>
            </Link>
          </div>
          
          <div className="flex items-center gap-3">
            <Link to="/pricing">

              <Button variant="ghost" size="sm" className="w-20 justify-center text-sm text-foreground hover:text-foreground hover:bg-accent">
                Pricing
              </Button>
            </Link>
            {isAuthenticated && user ? (
              <>
                <Link to="/dashboard">
                  <Button variant="ghost" size="sm" className="text-sm text-foreground hover:text-foreground hover:bg-accent">
                    Dashboard
                  </Button>
                </Link>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2 px-2 text-foreground hover:text-foreground hover:bg-accent">
                      <span className="text-sm">
                        {user.username}
                      </span>
                      {user.is_admin && (
                        <span className="rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                          Admin
                        </span>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="bg-card border shadow-lg">
                    {user.is_admin && (
                      <>
                        <DropdownMenuItem asChild>
                          <Link to="/users" className="w-full">
                            Users
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                      </>
                    )}
                    <DropdownMenuItem asChild>
                      <Link to="/billing" className="w-full">
                        Billing
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/account" className="w-full">
                        Account
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={logout}
                      className="text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400"
                    >
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            ) : (
              <Link to="/login">
                <Button variant="ghost" size="sm" className="w-20 justify-center text-sm text-foreground hover:text-foreground hover:bg-accent">
                  Login
                </Button>
              </Link>
            )}
            <ModeToggle />
          </div>
        </div>
      </header>
      
      <main className="px-6 py-6">
        {children}
      </main>
      
      <SetupModal />
    </div>
  );
};

export default Layout;