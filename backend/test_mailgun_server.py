# Import the necessary modules from FastAPI and uvicorn.
# FastAPI is the framework for building the API, and uvicorn is the server that runs it.
from fastapi import FastAPI, Form, HTTPException
import uvicorn
from typing import List

# Create a new FastAPI application instance.
app = FastAPI()

# Define the endpoint to mock the Mailgun API.
# The endpoint is a POST request to '/v3/{domain}/messages'.
# The path parameter `domain` is captured and can be used in the function.
# We expect the form data to include 'from', 'to', 'subject', and 'text'.
# Note: Mailgun's 'to' field can be a comma-separated string or a list. We'll handle both.
@app.post("/v3/{domain}/messages")
async def send_simple_message(
    # Use Form to indicate that these parameters are expected from form data.
    domain: str, # The domain from the URL path.
    sender: str = Form(..., alias="from"),  # 'from' is a reserved keyword in Python, so we use an alias.
    recipient: str = Form(..., alias="to"), # The 'to' field from the form data.
    subject: str = Form(...), # The 'subject' field from the form data.
    text_content: str = Form(..., alias="text") # The 'text' field from the form data.
):
    """
    This endpoint mocks the Mailgun API for sending simple messages.
    It receives form data and prints it to the console,
    simulating a successful request without actually sending an email.
    """
    print("--- Received an API request ---")
    print(f"Domain: {domain}")
    print(f"From: {sender}")
    # Split the recipients string by comma to handle multiple recipients.
    recipients = [r.strip() for r in recipient.split(',')]
    print("To:", ", ".join(recipients))
    print(f"Subject: {subject}")
    print(f"Text: {text_content}")
    print("--- End of request ---")

    # Return a JSON response that mimics a successful Mailgun API response.
    # This lets your client code think the request was successful.
    return {
        "message": "Queued. Thank you.",
        "id": "<<EMAIL>>"
    }

# This block allows the script to be run directly.
# It starts the uvicorn server with the FastAPI app.
# The host is set to '127.0.0.1' (localhost) and the port is 8000.
# The 'reload=True' option enables automatic reloading on code changes during development.
if __name__ == "__main__":
    uvicorn.run(app, host="127.0.0.1", port=9000, reload=False)
