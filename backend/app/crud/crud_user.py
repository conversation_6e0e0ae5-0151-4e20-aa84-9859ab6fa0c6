from sqlalchemy.orm import Session
from decimal import Decimal
from datetime import datetime, timedelta
import secrets
from app.models.user import User
from app.models.transaction import Transaction
from app.schemas.user import UserCreate
from app.core.security import get_password_hash
from app.services.email_service import send_verification_email
from app.core.config import get_settings


def get_user(db: Session, user_id: int):
    return db.query(User).filter(User.id == user_id).first()


def get_user_by_email(db: Session, email: str):
    return db.query(User).filter(User.email == email).first()


def get_users(db: Session, skip: int = 0, limit: int = 100):
    return db.query(User).offset(skip).limit(limit).all()


def _maybe_send_verification(db: Session, user: User) -> None:
    settings = get_settings()
    if not settings.USE_EMAIL:
        return
    try:
        token = user.verification_token or secrets.token_urlsafe(32)
        user.verification_token = token
        user.verification_token_expires_at = datetime.utcnow() + timedelta(hours=24)
        user.verification_sent_at = datetime.utcnow()
        db.add(user)
        db.commit()
        send_verification_email(user.email, user.username, token)
    except Exception as e:
        db.rollback()
        print(f"[EMAIL] Failed to queue verification email: {e}")


def create_user(db: Session, user: UserCreate, is_admin: bool = False):
    # Check if this is the first user - if so, make them admin
    user_count = db.query(User).count()
    if user_count == 0:
        is_admin = True

    # Sign-up fee: $4.99 added to user balance
    signup_fee = Decimal("4.99")

    hashed_password = get_password_hash(user.password)
    settings = get_settings()
    db_user = User(
        email=user.email,
        hashed_password=hashed_password,
        username=user.username,
        is_admin=is_admin,
        balance=signup_fee,
        is_verified=(not settings.USE_EMAIL),
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Create transaction record for sign-up fee
    signup_transaction = Transaction(
        user_id=db_user.id,
        amount=signup_fee,
        type="signup_fee",
        description="Sign-up fee - account credit"
    )
    db.add(signup_transaction)
    db.commit()

    # Send verification email if enabled
    _maybe_send_verification(db, db_user)

    return db_user


def delete_user(db: Session, user_id: int):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        db.delete(db_user)
        db.commit()
    return db_user


def get_admin_users(db: Session):
    return db.query(User).filter(User.is_admin == True).all()


def make_user_admin(db: Session, user_id: int):
    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user:
        db_user.is_admin = True
        db.commit()
        db.refresh(db_user)
    return db_user


def remove_admin_role(db: Session, user_id: int):
    # Check if this is the last admin
    admin_count = db.query(User).filter(User.is_admin == True).count()
    if admin_count <= 1:
        return None  # Cannot remove the last admin

    db_user = db.query(User).filter(User.id == user_id).first()
    if db_user and db_user.is_admin:
        db_user.is_admin = False
        db.commit()
        db.refresh(db_user)
    return db_user
