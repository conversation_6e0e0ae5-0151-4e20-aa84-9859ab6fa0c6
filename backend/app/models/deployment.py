from sqlalchemy import Column, <PERSON>, Inte<PERSON>, Foreign<PERSON>ey, DateTime, Numeric
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
from app.db.base import BaseModel
from typing import Optional, cast


class Deployment(BaseModel):
    __tablename__ = "deployments"

    user_id = Column(Integer, ForeignKey("users.id"))
    package = Column(String)

    # New server selection fields
    server_type = Column(String, default="new")  # 'new' | 'existing' | 'vps'
    vps_ip_address = Column(String, nullable=True)
    existing_server_id = Column(Integer, nullable=True)

    # Cloud fields (kept nullable; only required when server_type == 'new')
    cloud_provider = Column(String, nullable=True)
    region = Column(String, nullable=True)
    instance_type = Column(String, nullable=True)

    support_level = Column(String)
    # Hourly cost in cents (supports fractional cents)
    cost = Column(Numeric(10, 4))
    komodo_provider_endpoint = Column(String, nullable=True)
    komodo_api_key = Column(String, nullable=True)
    komodo_api_secret = Column(String, nullable=True)
    github_token = Column(String, nullable=True)
    client_id = Column(String, nullable=True)
    client_name = Column(String, nullable=True)
    domain = Column(String, nullable=True)
    admin_email = Column(String, nullable=True)
    admin_username = Column(String, nullable=True)
    admin_password = Column(String, nullable=True)
    admin_subdomain = Column(String, nullable=True)
    postgres_user = Column(String, nullable=True)
    postgres_password = Column(String, nullable=True)
    postgres_host = Column(String, nullable=True)
    github_repo = Column(String, nullable=True)
    crowdsec_enrollment_key = Column(String, nullable=True)
    static_page_domain = Column(String, nullable=True)
    static_page_subdomain = Column(String, nullable=True)
    oauth_client_id = Column(String, nullable=True)
    oauth_client_secret = Column(String, nullable=True)
    openai_api_key = Column(String, nullable=True)
    komodo_host_ip = Column(String, nullable=True)
    komodo_passkey = Column(String, nullable=True)
    maxmind_license_key = Column(String, nullable=True)
    firewall_name = Column(String, nullable=True)
    instance_tags: Optional[list[str]] = cast(Optional[list[str]], Column(ARRAY(String), nullable=True))
    instance_ip = Column(String, nullable=True)  # IP address of the created server instance
    user_ssh_key = Column(String, nullable=True)  # User's public SSH key for server access
    # Billing tracking
    last_billed_at = Column(DateTime(timezone=True), nullable=True, default=None)
    deleted_at = Column(DateTime(timezone=True), nullable=True, default=None)
    status = Column(String, nullable=False, default='CREATING')

    user = relationship("User", back_populates="deployments")
