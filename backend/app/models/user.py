from sqlalchemy import Column, String, Boolean, Numeric, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from app.db.base import BaseModel

class User(BaseModel):
    __tablename__ = "users"

    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_admin = Column(Boolean, default=False, nullable=False)
    # Account balance in EUR with 2 decimal places
    balance = Column(Numeric(12, 2), nullable=False, default=0)

    # Email verification and notification tracking
    is_verified = Column(Boolean, default=False, nullable=False)
    verification_token = Column(String, nullable=True)
    verification_token_expires_at = Column(DateTime, nullable=True)
    verification_sent_at = Column(DateTime, nullable=True)
    verified_at = Column(DateTime, nullable=True)

    last_low_balance_email_at = Column(DateTime, nullable=True)
    last_termination_email_at = Column(DateTime, nullable=True)

    deployments = relationship("Deployment", back_populates="user", uselist=True)
