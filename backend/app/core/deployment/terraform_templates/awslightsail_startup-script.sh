bash -c 'set -e; exec > >(tee /var/log/user-data.log) 2>&1; echo "Starting user data script execution at $(date)"; apt-get update; apt-get install -y ca-certificates curl gnupg lsb-release git postgresql-client; mkdir -p /etc/apt/keyrings; curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg; echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null; apt-get update; apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin; usermod -aG docker root; curl -L "https://github.com/docker/compose/releases/download/v2.20.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose; chmod +x /usr/local/bin/docker-compose; mkdir -p /etc/komodo/stacks; apt-get install -y python3 python3-pip; pip3 install requests toml pyyaml; echo "Starting komodo periphery at $(date)"; curl -sSL https://raw.githubusercontent.com/moghtech/komodo/main/scripts/setup-periphery.py | sudo python3; sed -i "s/allowed_ips = \[\]/allowed_ips = [\"{{ komodo_provider_ip }}\"]/" /etc/komodo/periphery.config.toml; sed -i "s/passkeys = \[\]/passkeys = [\"{{ komodo_passkey }}\"]/" /etc/komodo/periphery.config.toml; systemctl restart periphery; ufw --force enable; ufw allow OpenSSH; ufw allow 80/tcp; ufw allow 443/tcp; ufw allow 8120/tcp; ufw allow 9120/tcp; ufw allow 51820/udp; ufw default deny incoming; ufw default allow outgoing; ufw status verbose; systemctl enable periphery; echo "User data script finished at $(date)"'
