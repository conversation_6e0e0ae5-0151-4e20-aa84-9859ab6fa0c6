"""
Migration script to add verification and email notification columns to users table.
Run: python -m app.db.migrate_add_user_verification
"""
from sqlalchemy import create_engine, text
from app.core.config import get_settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def migrate():
    settings = get_settings()
    engine = create_engine(settings.DATABASE_URL)
    with engine.connect() as conn:
        # Add is_verified column
        result = conn.execute(text("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'is_verified'
        """))
        if result.fetchone() is None:
            logger.info("Adding is_verified column to users table...")
            conn.execute(text("ALTER TABLE users ADD COLUMN is_verified BOOLEAN NOT NULL DEFAULT FALSE"))
            conn.commit()
        else:
            logger.info("is_verified column already exists")

        # Add verification_token
        result = conn.execute(text("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'verification_token'
        """))
        if result.fetchone() is None:
            logger.info("Adding verification_token column to users table...")
            conn.execute(text("ALTER TABLE users ADD COLUMN verification_token VARCHAR NULL"))
            conn.commit()
        else:
            logger.info("verification_token column already exists")

        # Add verification_token_expires_at
        result = conn.execute(text("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'verification_token_expires_at'
        """))
        if result.fetchone() is None:
            logger.info("Adding verification_token_expires_at column to users table...")
            conn.execute(text("ALTER TABLE users ADD COLUMN verification_token_expires_at TIMESTAMP NULL"))
            conn.commit()
        else:
            logger.info("verification_token_expires_at column already exists")

        # Add verification_sent_at
        result = conn.execute(text("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'verification_sent_at'
        """))
        if result.fetchone() is None:
            logger.info("Adding verification_sent_at column to users table...")
            conn.execute(text("ALTER TABLE users ADD COLUMN verification_sent_at TIMESTAMP NULL"))
            conn.commit()
        else:
            logger.info("verification_sent_at column already exists")

        # Add verified_at
        result = conn.execute(text("""
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'users' AND column_name = 'verified_at'
        """))
        if result.fetchone() is None:
            logger.info("Adding verified_at column to users table...")
            conn.execute(text("ALTER TABLE users ADD COLUMN verified_at TIMESTAMP NULL"))
            conn.commit()
        else:
            logger.info("verified_at column already exists")

        # Add notification tracking columns
        for col in ("last_low_balance_email_at", "last_termination_email_at"):
            result = conn.execute(text(
                "SELECT column_name FROM information_schema.columns WHERE table_name = 'users' AND column_name = :col"),
                {"col": col}
            )
            if result.fetchone() is None:
                logger.info(f"Adding {col} column to users table...")
                conn.execute(text(f"ALTER TABLE users ADD COLUMN {col} TIMESTAMP NULL"))
                conn.commit()
            else:
                logger.info(f"{col} column already exists")

if __name__ == "__main__":
    migrate()

