from fastapi import APIRout<PERSON>, Depends, HTTPException, status, Query
from fastapi.security import OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import jwt, JW<PERSON>rror
from datetime import datetime
from app.db.session import get_db
from app.core.security import create_access_token, verify_password, ALGORITHM
from app.core.config import get_settings
from app.crud.crud_user import get_user_by_email
from app.schemas.token import Token
from app.schemas.user import User

router = APIRouter()
security = HTTPBearer()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    try:
        payload = jwt.decode(credentials.credentials, get_settings().SECRET_KEY, algorithms=[ALGORITHM])
        email = payload.get("sub")
        if email is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
        email_str: str = email # Cast to str after None check
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    user = get_user_by_email(db, email=email_str)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found",
        )
    return user

@router.post("/login", response_model=Token)
def login_for_access_token(db: Session = Depends(get_db), form_data: OAuth2PasswordRequestForm = Depends()):
    user = get_user_by_email(db, email=form_data.username)
    if not user or not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(
        subject=user.email
    )
    return {"access_token": access_token, "token_type": "bearer"}

@router.get("/verify-email")
def verify_email(token: str = Query(...), db: Session = Depends(get_db)):
    from app.models.user import User as UserModel
    
    # First try to find user by token
    db_user = db.query(UserModel).filter(UserModel.verification_token == token).first()
    
    # If no user found by token, check if any user was recently verified with this token
    # This handles the case where verification succeeded but page was reloaded/redirected
    if not db_user:
        # Look for recently verified users (within last 5 minutes) - this is a fallback for page reloads
        from datetime import timedelta
        recent_time = datetime.utcnow() - timedelta(minutes=5)
        recently_verified = db.query(UserModel).filter(
            UserModel.is_verified == True,
            UserModel.verified_at > recent_time,
            UserModel.verification_token.is_(None)
        ).first()
        
        if recently_verified:
            return {"message": "Email already verified successfully"}
        
        raise HTTPException(status_code=400, detail="Invalid or expired verification token")

    # Check if user is already verified
    if db_user.is_verified:
        return {"message": "Email already verified successfully"}

    # Check expiry if present
    if db_user.verification_token_expires_at and db_user.verification_token_expires_at < datetime.utcnow():
        raise HTTPException(status_code=400, detail="Verification token has expired")

    # Perform verification
    db_user.is_verified = True
    db_user.verified_at = datetime.utcnow()
    db_user.verification_token = None
    db_user.verification_token_expires_at = None
    db_user.verification_sent_at = None
    db.add(db_user)
    db.commit()

    return {"message": "Email verified successfully"}

async def get_admin_user(current_user: User = Depends(get_current_user)):
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user

@router.get("/me", response_model=User)
async def read_users_me(current_user: User = Depends(get_current_user)):
    return current_user
