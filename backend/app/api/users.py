from fastapi import APIRouter, Depends, HTTPException, Response, status, BackgroundTasks
from sqlalchemy.orm import Session
from app.schemas.user import User<PERSON><PERSON>, User, UserCreateAdmin
from app.db.session import get_db
from app.api.auth import get_current_user, get_admin_user
from app.crud.crud_user import (
    delete_user as crud_delete_user,
    get_users as crud_get_users,
    get_user as crud_get_user,
    create_user as crud_create_user,
    get_user_by_email,
    make_user_admin,
    remove_admin_role,
    get_admin_users
)
from app.models.deployment import Deployment as DeploymentModel
from app.core.deployment.deployment_manager import DeploymentManager
from typing import List
from app.services.email_service import send_verification_email
from datetime import datetime, timedelta

router = APIRouter()

# Admin-only: List all users
@router.get("/", response_model=List[User])
def read_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    users = crud_get_users(db, skip=skip, limit=limit)
    return users

# Public endpoint for user registration (first user becomes admin automatically)
@router.post("/", response_model=User)
def register(user: UserCreate, db: Session = Depends(get_db)):
    db_user = get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    return crud_create_user(db=db, user=user)

# Allow a user to resend their verification email
@router.post("/resend-verification")
def resend_verification(db: Session = Depends(get_db), current_user: User = Depends(get_current_user)):
    from app.models.user import User as UserModel
    db_user = db.query(UserModel).filter(UserModel.id == current_user.id).first()
    if not db_user:
        raise HTTPException(status_code=404, detail="User not found")
    if db_user.is_verified:
        return {"message": "Account already verified"}
    # Throttle resends to avoid abuse (e.g., 2 minutes)
    if db_user.verification_sent_at and (datetime.utcnow() - db_user.verification_sent_at).total_seconds() < 120:
        raise HTTPException(status_code=429, detail="Please wait before requesting another verification email")

    import secrets
    # Reuse existing valid token; otherwise generate new
    token = db_user.verification_token
    if not token or (db_user.verification_token_expires_at and db_user.verification_token_expires_at < datetime.utcnow()):
        token = secrets.token_urlsafe(32)
        db_user.verification_token = token
        db_user.verification_token_expires_at = datetime.utcnow() + timedelta(hours=24)
    # Update sent_at for throttling
    db_user.verification_sent_at = datetime.utcnow()
    db.add(db_user)
    db.commit()

    send_verification_email(db_user.email, db_user.username, token)
    return {"message": "Verification email sent"}

# Admin-only: Create new user with specified role
@router.post("/admin-create", response_model=User)
def create_user_as_admin(
    user: UserCreateAdmin,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    db_user = get_user_by_email(db, email=user.email)
    if db_user:
        raise HTTPException(status_code=400, detail="Email already registered")
    return crud_create_user(db=db, user=UserCreate(
        username=user.username,
        email=user.email,
        password=user.password
    ), is_admin=user.is_admin)

# Admin can view any user, regular users can only view themselves
@router.get("/{user_id}", response_model=User)
def read_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Check if user is admin or requesting their own profile
    if not current_user.is_admin and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    db_user = crud_get_user(db, user_id=user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

# Regular users can delete their own account, admins can delete any user
@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user_endpoint(
    user_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Check permissions
    if not current_user.is_admin and current_user.id != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    # Prevent deleting yourself if you're the last admin
    if current_user.id == user_id and current_user.is_admin:
        admin_count = len(get_admin_users(db))
        if admin_count <= 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete the last administrator"
            )

    # Get user's active deployments before deletion
    user_deployments = db.query(DeploymentModel).filter(
        DeploymentModel.user_id == user_id,
        DeploymentModel.status == 'ACTIVE'
    ).all()

    # Schedule deployment cleanup
    if user_deployments:
        deployment_manager = DeploymentManager()
        for deployment in user_deployments:
            background_tasks.add_task(deployment_manager.destroy, int(deployment.id))

    db_user = crud_delete_user(db, user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return Response(status_code=status.HTTP_204_NO_CONTENT)

# Admin-only: Make user an administrator
@router.patch("/{user_id}/make-admin", response_model=User)
def make_admin(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    db_user = make_user_admin(db, user_id)
    if db_user is None:
        raise HTTPException(status_code=404, detail="User not found")
    return db_user

# Admin-only: Remove admin role from user
@router.patch("/{user_id}/remove-admin", response_model=User)
def remove_admin(
    user_id: int,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    db_user = remove_admin_role(db, user_id)
    if db_user is None:
        raise HTTPException(status_code=400, detail="Cannot remove admin role - user not found or last admin")
    return db_user
