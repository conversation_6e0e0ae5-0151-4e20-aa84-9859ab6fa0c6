{"package": "Pangolin+AI", "package_description": "Secure MCP Gateway for AI Agentic Systems", "components": ["pangolin+", "crowdsec", "middleware-manager", "m<PERSON><PERSON><PERSON>", "nlweb", "static-page", "traefik-log-dashboard"], "details": [{"name": "pangolin+", "required_env": ["DOMAIN", "EMAIL", "ADMIN_SUBDOMAIN", "ADMIN_USERNAME", "ADMIN_PASSWORD"], "description": "Preconfigured tunnelled reverse proxy"}, {"name": "crowdsec", "required_env": ["CROWDSEC_ENROLLMENT_KEY"], "description": "Crowd sourced security protection"}, {"name": "m<PERSON><PERSON><PERSON>", "required_env": ["CLIENT_ID", "CLIENT_SECRET"], "description": "Provides <PERSON><PERSON><PERSON> for MCP server"}, {"name": "middleware-manager", "required_env": [], "description": "A service that allows you to add custom middleware to Pangolin / Traefik resources"}, {"name": "nlweb", "required_env": ["OPENAI_API_KEY"], "description": "Natural Language Web"}, {"name": "static-page", "required_env": ["STATIC_PAGE_SUBDOMAIN"], "description": "Creates a static landing page and configures Traefik routing for it"}, {"name": "traefik-log-dashboard", "required_env": ["MAXMIND_LICENSE_KEY"], "description": "Enhanced Traefik log dashboard with OTLP support and GeoIP capabilities"}]}